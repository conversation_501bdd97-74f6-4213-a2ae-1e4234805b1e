#!/usr/bin/env python3
"""
Script de test pour vérifier que le mode Light fonctionne correctement.
Teste les méthodes de style corrigées.
"""

import sys
import os

# Ajouter le répertoire Windows_and_Linux au path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'Windows_and_Linux'))

def test_ui_utils_styles():
    """Test les méthodes de style dans ui_utils.py"""
    print("🧪 Test des méthodes de style dans ui_utils.py...")
    
    from ui.ui_utils import get_effective_color_mode
    from ui.ui_utils import ThemedWidget
    
    # Simuler un mode light
    os.environ['FORCE_LIGHT_MODE'] = '1'
    
    # Créer un widget de test
    widget = ThemedWidget()
    
    # Tester les méthodes de style
    print(f"Mode effectif: {get_effective_color_mode()}")
    print(f"Label style: {widget.get_label_style()}")
    print(f"Dropdown style: {widget.get_dropdown_style()}")
    print(f"Radio style: {widget.get_radio_style()}")
    
    # Vérifier que les couleurs sont correctes pour le mode light
    label_style = widget.get_label_style()
    if '#333333' in label_style:
        print("✅ Label style correct pour mode Light")
    else:
        print("❌ Label style incorrect pour mode Light")
        
    dropdown_style = widget.get_dropdown_style()
    if 'white' in dropdown_style and '#000000' in dropdown_style:
        print("✅ Dropdown style correct pour mode Light")
    else:
        print("❌ Dropdown style incorrect pour mode Light")

def test_theme_manager():
    """Test le ThemeManager"""
    print("\n🧪 Test du ThemeManager...")
    
    from ui.ThemeManager import ThemeManager
    
    theme_manager = ThemeManager()
    styles = theme_manager.get_styles()
    
    print(f"Styles générés: {list(styles.keys())}")
    
    # Vérifier les couleurs pour le mode light
    if '#333333' in styles['label'] or '#000000' in styles['title']:
        print("✅ ThemeManager génère des styles corrects pour mode Light")
    else:
        print("❌ ThemeManager génère des styles incorrects pour mode Light")

def test_about_window():
    """Test les méthodes de style dans AboutWindow"""
    print("\n🧪 Test des méthodes de style dans AboutWindow...")
    
    try:
        from ui.AboutWindow import AboutWindow
        from ui.ui_utils import get_effective_color_mode
        
        print(f"Mode effectif: {get_effective_color_mode()}")
        print("✅ AboutWindow peut être importé sans erreur")
        
    except Exception as e:
        print(f"❌ Erreur lors de l'import d'AboutWindow: {e}")

if __name__ == "__main__":
    print("🎯 Test des corrections du mode Light\n")
    
    test_ui_utils_styles()
    test_theme_manager()
    test_about_window()
    
    print("\n✅ Tests terminés !")
